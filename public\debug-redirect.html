<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Redirect Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #6475e9;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #5a6bd8;
        }
        .button.outline {
            background: transparent;
            color: #6475e9;
            border: 1px solid #6475e9;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>🐛 Debug Redirect Issue</h1>
    <p>This tool helps debug why results pages redirect to dashboard.</p>

    <div class="test-card">
        <h2>Current State</h2>
        <div id="current-state">Loading...</div>
        <button class="button" onclick="checkCurrentState()">Refresh State</button>
    </div>

    <div class="test-card">
        <h2>Test Navigation</h2>
        <button class="button" onclick="testDirectNavigation('/results/result-001')">Test result-001</button>
        <button class="button" onclick="testDirectNavigation('/results/result-002')">Test result-002</button>
        <button class="button outline" onclick="testDirectNavigation('/dashboard')">Test Dashboard</button>
    </div>

    <div class="test-card">
        <h2>Authentication Debug</h2>
        <button class="button" onclick="checkAuthentication()">Check Auth State</button>
        <button class="button" onclick="clearAuth()">Clear Auth</button>
        <button class="button outline" onclick="setDemoAuth()">Set Demo Auth</button>
    </div>

    <div class="test-card">
        <h2>Debug Log</h2>
        <div id="log" class="log">Ready for debugging...</div>
        <button class="button outline" onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'Log cleared...';
        }

        function checkCurrentState() {
            const stateElement = document.getElementById('current-state');
            
            const state = {
                url: window.location.href,
                pathname: window.location.pathname,
                timestamp: new Date().toISOString(),
                localStorage: {
                    token: localStorage.getItem('token'),
                    user: localStorage.getItem('user'),
                    assessmentHistory: localStorage.getItem('assessment-history'),
                    navigationDebug: localStorage.getItem('navigation-debug')
                },
                cookies: document.cookie
            };
            
            stateElement.innerHTML = `
                <div style="font-family: monospace; font-size: 12px;">
                    <strong>URL:</strong> ${state.url}<br>
                    <strong>Pathname:</strong> ${state.pathname}<br>
                    <strong>Token:</strong> ${state.localStorage.token ? 'Present' : 'None'}<br>
                    <strong>User:</strong> ${state.localStorage.user ? 'Present' : 'None'}<br>
                    <strong>Cookies:</strong> ${state.cookies || 'None'}<br>
                    <strong>Timestamp:</strong> ${state.timestamp}
                </div>
            `;
            
            log('Current state checked');
        }

        function testDirectNavigation(url) {
            log(`🧭 Testing direct navigation to: ${url}`);
            
            // Monitor for redirects
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            let redirectCount = 0;
            const maxRedirects = 5;
            
            const monitorRedirects = () => {
                history.pushState = function(...args) {
                    redirectCount++;
                    log(`📍 Redirect ${redirectCount}: pushState to ${args[2]}`, redirectCount > 2 ? 'warning' : 'info');
                    
                    if (redirectCount > maxRedirects) {
                        log(`❌ Too many redirects detected (${redirectCount})`, 'error');
                        history.pushState = originalPushState;
                        history.replaceState = originalReplaceState;
                        return;
                    }
                    
                    return originalPushState.apply(this, args);
                };
                
                history.replaceState = function(...args) {
                    redirectCount++;
                    log(`📍 Redirect ${redirectCount}: replaceState to ${args[2]}`, redirectCount > 2 ? 'warning' : 'info');
                    
                    if (redirectCount > maxRedirects) {
                        log(`❌ Too many redirects detected (${redirectCount})`, 'error');
                        history.pushState = originalPushState;
                        history.replaceState = originalReplaceState;
                        return;
                    }
                    
                    return originalReplaceState.apply(this, args);
                };
            };
            
            monitorRedirects();
            
            // Open in new tab to avoid affecting current page
            const newWindow = window.open(url, '_blank');
            
            if (newWindow) {
                log(`✅ Navigation initiated to ${url}`, 'success');
                
                // Reset monitoring after a delay
                setTimeout(() => {
                    history.pushState = originalPushState;
                    history.replaceState = originalReplaceState;
                    log(`📊 Navigation test completed. Total redirects: ${redirectCount}`);
                }, 5000);
            } else {
                log(`❌ Failed to open window - popup blocked?`, 'error');
            }
        }

        function checkAuthentication() {
            log('🔐 Checking authentication state...');
            
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const cookies = document.cookie;
            
            log(`Token in localStorage: ${token ? 'Present' : 'None'}`);
            log(`User in localStorage: ${user ? 'Present' : 'None'}`);
            log(`Cookies: ${cookies || 'None'}`);
            
            if (token) {
                try {
                    const parsedUser = JSON.parse(user);
                    log(`User details: ${parsedUser.name} (${parsedUser.email})`, 'success');
                } catch (e) {
                    log(`Error parsing user data: ${e.message}`, 'error');
                }
            }
            
            // Check if token cookie is set
            const tokenCookie = cookies.split(';').find(c => c.trim().startsWith('token='));
            if (tokenCookie) {
                log(`Token cookie found: ${tokenCookie.trim()}`, 'success');
            } else {
                log(`No token cookie found`, 'warning');
            }
        }

        function clearAuth() {
            log('🗑️ Clearing authentication data...');
            
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
            
            log('✅ Authentication data cleared', 'success');
            checkCurrentState();
        }

        function setDemoAuth() {
            log('🎭 Setting demo authentication...');
            
            const demoToken = 'demo-token-' + Date.now();
            const demoUser = {
                id: 'demo-user',
                email: '<EMAIL>',
                name: 'Demo User',
                avatar: ''
            };
            
            localStorage.setItem('token', demoToken);
            localStorage.setItem('user', JSON.stringify(demoUser));
            document.cookie = `token=${demoToken}; path=/; max-age=${7 * 24 * 60 * 60}`;
            
            log('✅ Demo authentication set', 'success');
            checkCurrentState();
        }

        // Monitor page navigation events
        window.addEventListener('beforeunload', () => {
            log('🚪 Page is about to unload');
        });

        window.addEventListener('popstate', (event) => {
            log(`📍 Popstate event: ${window.location.pathname}`);
        });

        // Monitor for programmatic navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = function(...args) {
            log(`📍 History pushState: ${args[2]}`);
            return originalPushState.apply(this, args);
        };

        history.replaceState = function(...args) {
            log(`📍 History replaceState: ${args[2]}`);
            return originalReplaceState.apply(this, args);
        };

        // Initialize
        log('🚀 Debug tool loaded');
        checkCurrentState();
    </script>
</body>
</html>
