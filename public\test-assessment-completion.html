<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assessment Completion Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #6475e9;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #5a6bd8;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.outline {
            background: transparent;
            color: #6475e9;
            border: 1px solid #6475e9;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid #e9ecef;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; }
        .step { color: #6f42c1; font-weight: bold; }
        .result { background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🎯 Assessment Completion Test</h1>
    <p>This tool simulates the exact assessment completion flow to identify where the white screen issue occurs.</p>

    <div class="test-card">
        <h2>🧪 Assessment Flow Simulation</h2>
        <p>These tests simulate the exact steps that happen when a user completes an assessment:</p>
        
        <button class="button" onclick="testFullAssessmentFlow()">🚀 Test Complete Assessment Flow</button>
        <button class="button" onclick="testPartialAssessmentFlow()">⚡ Test Partial Assessment Flow</button>
        <button class="button outline" onclick="testNavigationOnly()">🧭 Test Navigation Only</button>
        <button class="button outline" onclick="testResultGeneration()">🎭 Test Result Generation</button>
    </div>

    <div class="test-card">
        <h2>🔧 Debug Tools</h2>
        <button class="button success" onclick="setupDemoData()">📦 Setup Demo Data</button>
        <button class="button" onclick="checkCurrentState()">🔍 Check Current State</button>
        <button class="button outline" onclick="testDirectNavigation()">🎯 Test Direct Navigation</button>
        <button class="button danger" onclick="clearAllData()">🗑️ Clear All Data</button>
    </div>

    <div class="test-card">
        <h2>📊 Test Results</h2>
        <div id="log" class="log">Ready to test assessment completion flow...</div>
        <button class="button outline" onclick="clearLog()">Clear Log</button>
        <button class="button outline" onclick="exportLog()">Export Log</button>
    </div>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            const logEntry = `[${timestamp}] ${message}`;
            
            logElement.innerHTML += `<div class="${className}">${logEntry}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            
            testResults.push({ timestamp, message, type });
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'Log cleared...';
            testResults = [];
        }

        function exportLog() {
            const blob = new Blob([JSON.stringify(testResults, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `assessment-test-log-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
            log('✅ Test log exported', 'success');
        }

        // Mock assessment answers (complete set)
        function generateMockAnswers(complete = true) {
            const answers = {};
            const totalQuestions = complete ? 132 : 66; // Half for partial test
            
            for (let i = 1; i <= totalQuestions; i++) {
                answers[i] = Math.floor(Math.random() * 5) + 1; // Random 1-5
            }
            
            return answers;
        }

        // Simulate assessment submission
        async function simulateAssessmentSubmission(answers, isFlexible = false) {
            log(`📤 Simulating assessment submission (${isFlexible ? 'flexible' : 'full'})...`, 'step');
            
            try {
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Generate result ID
                const resultId = 'result-' + Date.now().toString(36);
                
                // Create mock result
                const result = {
                    id: resultId,
                    userId: 'current-user',
                    createdAt: new Date().toISOString(),
                    status: 'completed',
                    assessment_data: {
                        riasec: { realistic: 3.5, investigative: 4.2, artistic: 3.8, social: 4.0, enterprising: 3.2, conventional: 2.8 },
                        ocean: { openness: 4.1, conscientiousness: 3.7, extraversion: 3.9, agreeableness: 4.3, neuroticism: 2.5 },
                        viaIs: { creativity: 4.2, curiosity: 4.0, judgment: 3.8, love_of_learning: 4.1, perspective: 3.9 }
                    },
                    persona_profile: {
                        title: 'Test Assessment Result',
                        description: 'This is a test result generated for debugging purposes.',
                        careerRecommendation: ['Software Developer', 'UX Designer', 'Data Analyst'],
                        strengths: ['Problem Solving', 'Creativity', 'Analytical Thinking'],
                        weaknesses: ['Time Management', 'Public Speaking'],
                        personalityTraits: ['Innovative', 'Detail-oriented', 'Collaborative']
                    }
                };
                
                // Save to localStorage
                localStorage.setItem(`assessment-result-${resultId}`, JSON.stringify(result));
                log(`✅ Assessment result saved to localStorage: ${resultId}`, 'success');
                
                // Add to history
                const historyItem = {
                    id: Date.now(),
                    nama: result.persona_profile.title,
                    tipe: "Personality Assessment",
                    tanggal: new Date().toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    }),
                    status: "Selesai",
                    resultId: resultId
                };
                
                const existingHistory = JSON.parse(localStorage.getItem('assessment-history') || '[]');
                existingHistory.unshift(historyItem);
                localStorage.setItem('assessment-history', JSON.stringify(existingHistory));
                log(`✅ Assessment added to history`, 'success');
                
                return { resultId, personaTitle: result.persona_profile.title };
                
            } catch (error) {
                log(`❌ Assessment submission failed: ${error.message}`, 'error');
                throw error;
            }
        }

        // Simulate navigation
        async function simulateNavigation(destination, method = 'router.push') {
            log(`🧭 Simulating navigation to: ${destination} (method: ${method})`, 'step');
            
            try {
                if (method === 'window.location.href') {
                    // Test if we can navigate
                    const testWindow = window.open(destination, '_blank');
                    if (testWindow) {
                        log(`✅ Navigation successful via ${method}`, 'success');
                        setTimeout(() => testWindow.close(), 2000);
                        return true;
                    } else {
                        log(`❌ Navigation failed - popup blocked`, 'error');
                        return false;
                    }
                } else {
                    // Simulate router.push
                    log(`✅ Router.push would navigate to: ${destination}`, 'success');
                    return true;
                }
            } catch (error) {
                log(`❌ Navigation failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Test complete assessment flow
        async function testFullAssessmentFlow() {
            log('🚀 Starting COMPLETE assessment flow test...', 'step');
            log('=' * 50, 'info');
            
            try {
                // Step 1: Generate complete answers
                log('1️⃣ Generating complete assessment answers...', 'step');
                const answers = generateMockAnswers(true);
                log(`✅ Generated ${Object.keys(answers).length} answers`, 'success');
                
                // Step 2: Submit assessment
                log('2️⃣ Submitting assessment...', 'step');
                const { resultId, personaTitle } = await simulateAssessmentSubmission(answers, false);
                
                // Step 3: Navigate to results
                log('3️⃣ Navigating to results page...', 'step');
                const resultsUrl = `/results/${resultId}`;
                const navigationSuccess = await simulateNavigation(resultsUrl, 'router.push');
                
                if (!navigationSuccess) {
                    log('⚠️ Router navigation failed, trying window.location.href...', 'warning');
                    await simulateNavigation(resultsUrl, 'window.location.href');
                }
                
                // Step 4: Verify result
                log('4️⃣ Verifying result data...', 'step');
                const savedResult = localStorage.getItem(`assessment-result-${resultId}`);
                if (savedResult) {
                    log(`✅ Result verified in localStorage`, 'success');
                    log(`🎯 Result URL: ${window.location.origin}${resultsUrl}`, 'result');
                } else {
                    log(`❌ Result not found in localStorage`, 'error');
                }
                
                log('🎉 Complete assessment flow test finished!', 'success');
                
            } catch (error) {
                log(`❌ Complete assessment flow test failed: ${error.message}`, 'error');
            }
        }

        // Test partial assessment flow
        async function testPartialAssessmentFlow() {
            log('⚡ Starting PARTIAL assessment flow test...', 'step');
            log('=' * 50, 'info');
            
            try {
                // Step 1: Generate partial answers
                log('1️⃣ Generating partial assessment answers...', 'step');
                const answers = generateMockAnswers(false);
                log(`✅ Generated ${Object.keys(answers).length} answers (partial)`, 'success');
                
                // Step 2: Submit assessment (flexible)
                log('2️⃣ Submitting assessment (flexible validation)...', 'step');
                const { resultId, personaTitle } = await simulateAssessmentSubmission(answers, true);
                
                // Step 3: Navigate to results
                log('3️⃣ Navigating to results page...', 'step');
                const resultsUrl = `/results/${resultId}`;
                const navigationSuccess = await simulateNavigation(resultsUrl, 'router.push');
                
                if (!navigationSuccess) {
                    log('⚠️ Router navigation failed, trying window.location.href...', 'warning');
                    await simulateNavigation(resultsUrl, 'window.location.href');
                }
                
                log('🎉 Partial assessment flow test finished!', 'success');
                
            } catch (error) {
                log(`❌ Partial assessment flow test failed: ${error.message}`, 'error');
            }
        }

        // Test navigation only
        async function testNavigationOnly() {
            log('🧭 Testing navigation to existing results...', 'step');
            
            const testUrls = [
                '/results/result-001',
                '/results/result-002',
                '/dashboard',
                '/assessment'
            ];
            
            for (const url of testUrls) {
                await simulateNavigation(url, 'window.location.href');
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // Test result generation
        async function testResultGeneration() {
            log('🎭 Testing result generation...', 'step');
            
            try {
                const answers = generateMockAnswers(true);
                const { resultId } = await simulateAssessmentSubmission(answers, false);
                
                // Test if result can be retrieved
                const savedResult = localStorage.getItem(`assessment-result-${resultId}`);
                const parsedResult = JSON.parse(savedResult);
                
                log(`✅ Result generated successfully:`, 'success');
                log(`   ID: ${parsedResult.id}`, 'info');
                log(`   Title: ${parsedResult.persona_profile.title}`, 'info');
                log(`   Status: ${parsedResult.status}`, 'info');
                log(`   Created: ${parsedResult.createdAt}`, 'info');
                
            } catch (error) {
                log(`❌ Result generation failed: ${error.message}`, 'error');
            }
        }

        // Setup demo data
        function setupDemoData() {
            log('📦 Setting up demo data...', 'step');
            
            // Demo results
            const demoResults = [
                {
                    id: 'result-001',
                    userId: 'current-user',
                    createdAt: '2024-01-15T10:30:00Z',
                    status: 'completed',
                    assessment_data: {
                        riasec: { realistic: 45, investigative: 85, artistic: 72, social: 38, enterprising: 65, conventional: 42 },
                        ocean: { openness: 88, conscientiousness: 67, extraversion: 45, agreeableness: 72, neuroticism: 25 },
                        viaIs: { creativity: 92, curiosity: 89, judgment: 78, loveOfLearning: 85, perspective: 74 }
                    },
                    persona_profile: {
                        title: 'The Innovative Researcher',
                        description: 'You are a highly creative and analytical individual...',
                        careerRecommendation: ['Research Scientist', 'UX Designer', 'Product Manager'],
                        strengths: ['Creative Problem Solving', 'Analytical Thinking', 'Innovation'],
                        weaknesses: ['Time Management', 'Routine Tasks'],
                        personalityTraits: ['Curious', 'Independent', 'Visionary']
                    }
                },
                {
                    id: 'result-002',
                    userId: 'current-user',
                    createdAt: '2024-01-16T14:20:00Z',
                    status: 'completed',
                    assessment_data: {
                        riasec: { realistic: 32, investigative: 58, artistic: 89, social: 76, enterprising: 45, conventional: 28 },
                        ocean: { openness: 92, conscientiousness: 54, extraversion: 78, agreeableness: 85, neuroticism: 18 },
                        viaIs: { creativity: 95, curiosity: 82, judgment: 71, loveOfLearning: 88, perspective: 79 }
                    },
                    persona_profile: {
                        title: 'The Creative Collaborator',
                        description: 'You are a highly creative and socially engaged individual...',
                        careerRecommendation: ['Creative Director', 'Marketing Manager', 'Social Worker'],
                        strengths: ['Creativity', 'Empathy', 'Communication'],
                        weaknesses: ['Detail Orientation', 'Structure'],
                        personalityTraits: ['Artistic', 'Empathetic', 'Expressive']
                    }
                }
            ];
            
            // Save demo results
            demoResults.forEach(result => {
                localStorage.setItem(`assessment-result-${result.id}`, JSON.stringify(result));
                log(`✅ Demo result saved: ${result.id} - ${result.persona_profile.title}`, 'success');
            });
            
            // Setup demo history
            const demoHistory = demoResults.map((result, index) => ({
                id: Date.now() + index,
                nama: result.persona_profile.title,
                tipe: "Personality Assessment",
                tanggal: new Date(result.createdAt).toLocaleDateString('id-ID'),
                status: "Selesai",
                resultId: result.id
            }));
            
            localStorage.setItem('assessment-history', JSON.stringify(demoHistory));
            log(`✅ Demo history saved with ${demoHistory.length} items`, 'success');
        }

        // Check current state
        function checkCurrentState() {
            log('🔍 Checking current application state...', 'step');
            
            try {
                // Check localStorage
                const history = localStorage.getItem('assessment-history');
                const progress = localStorage.getItem('assessment-progress');
                const navigationDebug = localStorage.getItem('navigation-debug');
                
                log(`📊 Assessment History: ${history ? JSON.parse(history).length + ' items' : 'None'}`, 'info');
                log(`📈 Assessment Progress: ${progress ? 'Present' : 'None'}`, 'info');
                log(`🐛 Navigation Debug: ${navigationDebug ? JSON.parse(navigationDebug).length + ' events' : 'None'}`, 'info');
                
                // Check results
                const resultKeys = Object.keys(localStorage).filter(key => key.startsWith('assessment-result-'));
                log(`🎯 Assessment Results: ${resultKeys.length} found`, 'info');
                
                resultKeys.forEach(key => {
                    const resultId = key.replace('assessment-result-', '');
                    const result = JSON.parse(localStorage.getItem(key));
                    log(`   - ${resultId}: ${result.persona_profile?.title || 'Unknown'}`, 'info');
                });
                
                // Check current page
                log(`📍 Current URL: ${window.location.href}`, 'info');
                log(`🌐 User Agent: ${navigator.userAgent.substring(0, 100)}...`, 'info');
                
            } catch (error) {
                log(`❌ Error checking state: ${error.message}`, 'error');
            }
        }

        // Test direct navigation
        function testDirectNavigation() {
            log('🎯 Testing direct navigation to results pages...', 'step');
            
            const testUrls = [
                'http://localhost:3001/results/result-001',
                'http://localhost:3001/results/result-002',
                'http://localhost:3001/dashboard'
            ];
            
            testUrls.forEach(url => {
                log(`🔗 Opening: ${url}`, 'info');
                window.open(url, '_blank');
            });
        }

        // Clear all data
        function clearAllData() {
            if (confirm('Are you sure you want to clear all localStorage data?')) {
                log('🗑️ Clearing all localStorage data...', 'step');
                
                const keys = Object.keys(localStorage);
                let cleared = 0;
                
                keys.forEach(key => {
                    if (key.startsWith('assessment-') || key.startsWith('navigation-')) {
                        localStorage.removeItem(key);
                        cleared++;
                    }
                });
                
                log(`✅ Cleared ${cleared} items from localStorage`, 'success');
            }
        }

        // Initialize
        log('🚀 Assessment Completion Test Tool loaded', 'success');
        log('Click the buttons above to test different aspects of the assessment flow', 'info');
        
        // Auto-setup demo data
        setTimeout(() => {
            setupDemoData();
        }, 1000);
    </script>
</body>
</html>
